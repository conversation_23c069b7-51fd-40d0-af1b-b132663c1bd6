import { json } from '@sveltejs/kit';
import { getAuthToken } from '$lib/server/mpesa';

export async function GET() {
	console.log('🧪 Testing M-Pesa configuration...');
	
	try {
		// Test authentication
		const token = await getAuthToken();
		
		if (token) {
			console.log('✅ M-Pesa authentication successful');
			return json({ 
				success: true, 
				message: 'M-Pesa configuration is valid and authentication successful',
				tokenLength: token.length
			});
		} else {
			console.error('❌ M-Pesa authentication failed - no token received');
			return json({ 
				success: false, 
				error: 'Authentication failed - no token received' 
			}, { status: 500 });
		}
	} catch (error) {
		console.error('❌ M-Pesa test failed:', error);
		return json({ 
			success: false, 
			error: error instanceof Error ? error.message : 'Unknown error occurred',
			details: 'Check server logs for more information'
		}, { status: 500 });
	}
}

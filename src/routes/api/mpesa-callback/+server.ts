import { json } from '@sveltejs/kit';
import { getDb } from '$lib/server/database';

export async function POST({ request }) {
	console.log('📞 Received M-Pesa callback');

	try {
		const data = await request.json();
		console.log('📥 Callback data:', JSON.stringify(data, null, 2));

		// Extract the callback data
		const { Body } = data;

		if (!Body || !Body.stkCallback) {
			console.error('❌ Invalid callback structure - missing Body or stkCallback');
			return json({ success: false, error: 'Invalid callback structure' }, { status: 400 });
		}

		const { stkCallback } = Body;
		console.log(`📊 Callback result code: ${stkCallback.ResultCode}`);
		console.log(`📝 Callback description: ${stkCallback.ResultDesc}`);

		if (stkCallback.ResultCode === 0) {
			console.log('✅ Payment successful - processing callback');

			// Payment successful
			const callbackMetadata = stkCallback.CallbackMetadata?.Item;

			if (!callbackMetadata) {
				console.error('❌ Missing callback metadata');
				return json({ success: false, error: 'Missing callback metadata' }, { status: 400 });
			}

			// Extract metadata
			const amount = callbackMetadata.find((item) => item.Name === 'Amount')?.Value;
			const mpesaReceiptNumber = callbackMetadata.find(
				(item) => item.Name === 'MpesaReceiptNumber'
			)?.Value;
			const transactionDate = callbackMetadata.find(
				(item) => item.Name === 'TransactionDate'
			)?.Value;
			const phoneNumber = callbackMetadata.find((item) => item.Name === 'PhoneNumber')?.Value;

			console.log(`💰 Amount: KES ${amount}`);
			console.log(`🧾 Receipt: ${mpesaReceiptNumber}`);
			console.log(`📅 Date: ${transactionDate}`);
			console.log(
				`📱 Phone: ${phoneNumber?.toString().replace(/(\d{3})\d{6}(\d{3})/, '$1****$2')}`
			);

			// Extract the project ID from the account reference
			const accountReference = callbackMetadata.find(
				(item) => item.Name === 'AccountReference'
			)?.Value;

			if (!accountReference) {
				console.error('❌ Missing account reference in callback');
				return json({ success: false, error: 'Missing account reference' }, { status: 400 });
			}

			const projectId = accountReference.replace('Donation-', '');
			console.log(`🎯 Project ID: ${projectId}`);

			// Update the donation record
			const db = await getDb();

			// Find the pending donation by phone number hash and project ID
			const pendingDonation = await db.get(
				`SELECT id FROM donations
         WHERE project_id = ? AND mpesa_receipt_number IS NULL
         ORDER BY donation_date DESC LIMIT 1`,
				[projectId]
			);

			if (pendingDonation) {
				console.log(`🔄 Updating donation record: ${pendingDonation.id}`);
				await db.run(
					`UPDATE donations
           SET mpesa_receipt_number = ?,
               amount = ?
           WHERE id = ?`,
					[mpesaReceiptNumber, amount, pendingDonation.id]
				);
				console.log('✅ Donation record updated successfully');
			} else {
				console.warn('⚠️ No pending donation found for project:', projectId);
			}

			return json({ success: true });
		} else {
			// Payment failed
			console.error(
				`❌ M-Pesa payment failed (Code: ${stkCallback.ResultCode}):`,
				stkCallback.ResultDesc
			);
			return json({ success: false, error: stkCallback.ResultDesc });
		}
	} catch (error) {
		console.error('❌ Error processing M-Pesa callback:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
}
